import React, { useState } from 'react';
import UserDashboardLayout from './UserDashboardLayout';
import UserSideNav from './UserSideNav';
import '../../styles/components/user-dashboard/Transactions.css';

// Import assets
// import cardCoinIcon from '../../assets/card-coin.png';

const Transactions: React.FC = () => {
  const [activeTab, setActiveTab] = useState('transactions');
  const [showBalances, setShowBalances] = useState(true);

  const toggleBalances = () => {
    setShowBalances(!showBalances);
  };

  return (
    <UserDashboardLayout className="transactions-container">
      <div className="transactions-content">
        {/* User Greeting Header */}
        <div className="user-header">
          <div className="user-greeting">
            <h1 className="greeting-text">Hi, Oluwatosin 👋</h1>
            <p className="greeting-subtitle">Here is your transaction history</p>

            <div className="header-controls">
              <button className="stake-esvc-btn">
                <img src={cardCoinIcon} alt="Stake" className="btn-icon" />
                Stake ESVC
              </button>
              
              <div className="balance-toggle">
                <span className="toggle-label">Show balances</span>
                <label className="toggle-switch">
                  <input
                    type="checkbox"
                    checked={showBalances}
                    onChange={toggleBalances}
                  />
                  <span className="toggle-slider"></span>
                </label>
                <span className="toggle-label">Hide balances</span>
              </div>
            </div>
          </div>
        </div>

        <div className="dashboard-layout">
          <UserSideNav activeTab={activeTab} onTabChange={setActiveTab} />
          
          <div className="dashboard-content">
            <div className="transactions-section">
              <div className="transactions-card">
                <h2 className="section-title">Transaction History</h2>
                <p className="section-description">
                  Your transaction history will appear here once you start staking and making withdrawals.
                </p>
                
                <div className="empty-state">
                  <div className="empty-icon">📊</div>
                  <h3 className="empty-title">No Transactions Yet</h3>
                  <p className="empty-description">
                    Start staking ESVC to see your transaction history here.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </UserDashboardLayout>
  );
};

export default Transactions;
