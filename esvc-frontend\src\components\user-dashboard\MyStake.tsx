import React, { useState } from 'react';
import UserDashboardLayout from './UserDashboardLayout';
import UserSideNav from './UserSideNav';
import '../../styles/components/user-dashboard/MyStake.css';

// Import assets
import cardCoinIcon from '../../assets/card-coin.png';

const MyStake: React.FC = () => {
  const [activeTab, setActiveTab] = useState('my-stake');
  const [showBalances, setShowBalances] = useState(true);
  const [selectedWallet, setSelectedWallet] = useState('Wallet 1 (90,000 stake)');

  // Sample data
  const walletOptions = [
    'Wallet 1 (90,000 stake)',
    'Wallet 2 (45,000 stake)',
    'Wallet 3 (25,000 stake)'
  ];

  const stakeData = {
    amountStaked: 90000,
    roiEarned: 700,
    totalExpectedRoi: 2000,
    status: 'Active',
    lockPeriod: '12 months',
    dateStaked: 'Jan 3, 2025',
    unstakeDate: 'Jan 3, 2026'
  };

  const toggleBalances = () => {
    setShowBalances(!showBalances);
  };

  const formatCurrency = (amount: number) => {
    return showBalances ? `$${amount.toLocaleString()}` : '****';
  };

  return (
    <UserDashboardLayout className="my-stake-container">
      <div className="my-stake-content">
        {/* User Greeting Header */}
        <div className="user-header">
          <div className="user-greeting">
            <h1 className="greeting-text">Hi, Oluwatosin 👋</h1>
            <p className="greeting-subtitle">Here is your staking overview</p>

            <div className="header-controls">
              <button className="stake-esvc-btn">
                <img src={cardCoinIcon} alt="Stake" className="btn-icon" />
                Stake ESVC
              </button>
              
              <div className="balance-toggle">
                <span className="toggle-label">Show balances</span>
                <label className="toggle-switch">
                  <input
                    type="checkbox"
                    checked={showBalances}
                    onChange={toggleBalances}
                  />
                  <span className="toggle-slider"></span>
                </label>
                <span className="toggle-label">Hide balances</span>
              </div>
            </div>
          </div>
        </div>

        <div className="dashboard-layout">
          <UserSideNav activeTab={activeTab} onTabChange={setActiveTab} />
          
          <div className="dashboard-content">
            {/* Wallet Selector */}
            <div className="wallet-selector-section">
              <div className="wallet-selector-card">
                <div className="wallet-selector-header">
                  <select 
                    className="wallet-dropdown"
                    value={selectedWallet}
                    onChange={(e) => setSelectedWallet(e.target.value)}
                  >
                    {walletOptions.map((wallet, index) => (
                      <option key={index} value={wallet}>{wallet}</option>
                    ))}
                  </select>
                </div>
              </div>
            </div>

            {/* Stake Details Card */}
            <div className="stake-details-section">
              <div className="stake-details-card">
                <div className="stake-header">
                  <div className="stake-title-section">
                    <h3 className="stake-title">7ESVC</h3>
                    <p className="stake-subtitle">at {formatCurrency(stakeData.amountStaked)}</p>
                  </div>
                  
                  <div className="stake-stats">
                    <div className="stat-item">
                      <span className="stat-value">{formatCurrency(stakeData.roiEarned)}</span>
                      <span className="stat-label">ROI earned so far</span>
                    </div>
                    <div className="stat-item">
                      <span className="stat-value">{formatCurrency(stakeData.totalExpectedRoi)}</span>
                      <span className="stat-label">Total Expected ROI</span>
                    </div>
                  </div>
                  
                  <button className="withdraw-earned-btn">
                    Withdraw Earned ROI
                  </button>
                </div>

                <div className="stake-info-grid">
                  <div className="info-row">
                    <span className="info-label">Status</span>
                    <span className="info-value status-active">{stakeData.status}</span>
                  </div>
                  
                  <div className="info-row">
                    <span className="info-label">Lock Period</span>
                    <span className="info-value">{stakeData.lockPeriod}</span>
                  </div>
                  
                  <div className="info-row">
                    <span className="info-label">Date Staked</span>
                    <span className="info-value">{stakeData.dateStaked}</span>
                  </div>
                  
                  <div className="info-row">
                    <span className="info-label">Unstake Date</span>
                    <span className="info-value">{stakeData.unstakeDate}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </UserDashboardLayout>
  );
};

export default MyStake;
