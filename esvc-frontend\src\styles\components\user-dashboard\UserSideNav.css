/* User SideNav Container */
.user-sidenav-container {
  width: 280px;
  background: rgba(38, 38, 38, 0.8);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 16px;
  height: fit-content;
  position: sticky;
  top: 140px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.user-sidenav-list {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.user-sidenav-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px 20px;
  background: none;
  border: none;
  border-radius: 8px;
  color: #CCCCCC;
  font-family: 'Montserrat', sans-serif;
  font-size: 15px;
  font-weight: 600;
  line-height: 1.5;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: left;
  width: 100%;
  position: relative;
}

.user-sidenav-item:hover {
  background: rgba(255, 255, 255, 0.05);
  color: #FFFFFF;
}

.user-sidenav-item.active {
  background: #BF4129;
  color: #FFFFFF;
  box-shadow: 0 4px 16px rgba(191, 65, 41, 0.3);
}

.user-sidenav-icon {
  width: 24px;
  height: 24px;
  object-fit: contain;
  filter: brightness(0.8);
  transition: filter 0.3s ease;
}

.user-sidenav-item:hover .user-sidenav-icon,
.user-sidenav-item.active .user-sidenav-icon {
  filter: brightness(1);
}

.user-sidenav-label {
  font-family: 'Montserrat', sans-serif;
  font-weight: 600;
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .user-sidenav-container {
    width: 100%;
    position: static;
    order: 1;
    top: auto;
    padding: 8px 12px;
    margin-bottom: 20px;
    margin-top: 10px;
    border-radius: 12px;
    height: 60px;
    display: flex;
    align-items: center;
  }

  .user-sidenav-list {
    flex-direction: row;
    overflow-x: auto;
    gap: 12px;
    padding: 0 8px;
    scrollbar-width: none;
    -ms-overflow-style: none;
    align-items: center;
    justify-content: flex-start;
    width: 100%;
    height: 100%;
    scroll-behavior: smooth;
  }

  .user-sidenav-list::-webkit-scrollbar {
    display: none;
  }

  .user-sidenav-item {
    white-space: nowrap;
    min-width: 120px;
    max-width: 140px;
    padding: 10px 16px;
    flex-shrink: 0;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    line-height: 1.2;
    height: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    box-sizing: border-box;
    background: #404040;
    border: 1px solid #525252;
  }

  .user-sidenav-item:hover {
    background: #525252;
    border-color: #666666;
  }

  .user-sidenav-item.active {
    background: #BF4129;
    border-color: #BF4129;
  }

  .user-sidenav-icon {
    width: 20px;
    height: 20px;
  }

  .user-sidenav-label {
    font-size: 13px;
  }
}
