/* My Stake Page Styles */
.my-stake-container {
  min-height: 100vh;
  background: #0A0A0A;
}

.my-stake-content {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

/* User Header - Reusing from Overview */
.user-header {
  position: relative;
  margin-bottom: 40px;
}

.user-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: -50vw;
  right: -50vw;
  height: 200px;
  background: linear-gradient(135deg, #BF4129 0%, #8B2F1F 100%);
  z-index: -1;
}

.user-greeting {
  padding: 40px 0;
  position: relative;
  flex: 1;
  position: relative;
}

.greeting-text {
  font-family: 'Montserrat', sans-serif;
  font-size: 48px;
  font-weight: 700;
  color: #FFFFFF;
  margin: 0 0 8px 0;
  line-height: 1.2;
}

.greeting-subtitle {
  font-family: 'Montserrat', sans-serif;
  font-size: 18px;
  color: #CCCCCC;
  margin: 0 0 32px 0;
  font-weight: 400;
}

.header-controls {
  position: absolute;
  top: 0;
  right: 0;
  display: flex;
  align-items: center;
  gap: 16px;
  flex-wrap: wrap;
}

/* Balance Toggle */
.balance-toggle {
  display: flex;
  align-items: center;
  gap: 12px;
}

.toggle-label {
  font-size: 14px;
  color: #CCCCCC;
  font-family: 'Montserrat', sans-serif;
  font-weight: 500;
}

.toggle-switch {
  position: relative;
  width: 48px;
  height: 24px;
  cursor: pointer;
}

.toggle-switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.toggle-slider {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: #404040;
  border-radius: 24px;
  transition: 0.3s;
}

.toggle-slider:before {
  position: absolute;
  content: "";
  height: 18px;
  width: 18px;
  left: 3px;
  bottom: 3px;
  background: #FFFFFF;
  border-radius: 50%;
  transition: 0.3s;
}

.toggle-switch input:checked + .toggle-slider {
  background: #BF4129;
}

.toggle-switch input:checked + .toggle-slider:before {
  transform: translateX(24px);
}

/* Stake Button */
.stake-esvc-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  background: #BF4129;
  color: #FFFFFF;
  border: none;
  border-radius: 8px;
  padding: 12px 20px;
  font-family: 'Montserrat', sans-serif;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.stake-esvc-btn:hover {
  background: #A63622;
  transform: translateY(-1px);
}

.btn-icon {
  width: 20px;
  height: 20px;
}

/* Dashboard Layout */
.dashboard-layout {
  display: flex;
  gap: 32px;
  align-items: flex-start;
}

.dashboard-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 24px;
}

/* Wallet Selector Section */
.wallet-selector-section {
  width: 100%;
}

.wallet-selector-card {
  background: #1D1104;
  border-radius: 16px;
  padding: 24px;
  border: 1px solid rgba(191, 65, 41, 0.1);
}

.wallet-dropdown {
  width: 100%;
  background: #262626;
  border: 1px solid #404040;
  border-radius: 8px;
  padding: 12px 16px;
  color: #FFFFFF;
  font-family: 'Montserrat', sans-serif;
  font-size: 16px;
  cursor: pointer;
  outline: none;
}

.wallet-dropdown:focus {
  border-color: #BF4129;
}

/* Stake Details Section */
.stake-details-section {
  width: 100%;
}

.stake-details-card {
  background: #1D1104;
  border-radius: 16px;
  padding: 32px;
  border: 1px solid rgba(191, 65, 41, 0.1);
}

.stake-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 32px;
  flex-wrap: wrap;
  gap: 24px;
}

.stake-title-section {
  flex: 1;
  min-width: 200px;
}

.stake-title {
  font-family: 'Montserrat', sans-serif;
  font-size: 32px;
  font-weight: 700;
  color: #FFFFFF;
  margin: 0 0 8px 0;
}

.stake-subtitle {
  font-family: 'Montserrat', sans-serif;
  font-size: 16px;
  color: #CCCCCC;
  margin: 0;
}

.stake-stats {
  display: flex;
  gap: 32px;
  flex: 2;
  justify-content: center;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.stat-value {
  font-family: 'Montserrat', sans-serif;
  font-size: 24px;
  font-weight: 700;
  color: #FFFFFF;
  margin-bottom: 4px;
}

.stat-label {
  font-family: 'Montserrat', sans-serif;
  font-size: 14px;
  color: #CCCCCC;
  font-weight: 500;
}

.withdraw-earned-btn {
  background: #c6741b;
  color: #FFFFFF;
  border: none;
  border-radius: 8px;
  padding: 12px 20px;
  font-family: 'Montserrat', sans-serif;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  white-space: nowrap;
}

.withdraw-earned-btn:hover {
  background: #b8661a;
  transform: translateY(-1px);
}

/* Stake Info Grid */
.stake-info-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;
}

.info-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.info-label {
  font-family: 'Montserrat', sans-serif;
  font-size: 14px;
  color: #CCCCCC;
  font-weight: 500;
}

.info-value {
  font-family: 'Montserrat', sans-serif;
  font-size: 16px;
  color: #FFFFFF;
  font-weight: 600;
}

.status-active {
  color: #22C55E;
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .user-header::before {
    height: 320px;
  }

  .header-controls {
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 8px;
    flex-wrap: wrap;
    padding: 0;
  }

  .balance-toggle {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    gap: 8px;
    min-width: auto;
  }

  .stake-esvc-btn {
    width: auto;
    min-width: 140px;
    justify-content: center;
    padding: 12px 16px;
    font-size: 14px;
    margin: 0;
  }

  .dashboard-layout {
    flex-direction: column;
    gap: 20px;
  }

  .stake-header {
    flex-direction: column;
    align-items: stretch;
    gap: 20px;
  }

  .stake-stats {
    justify-content: space-around;
    gap: 16px;
  }

  .stake-info-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .greeting-text {
    font-size: 32px;
  }

  .greeting-subtitle {
    font-size: 16px;
  }
}
