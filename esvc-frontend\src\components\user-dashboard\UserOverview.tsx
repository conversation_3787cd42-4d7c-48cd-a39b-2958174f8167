import React, { useState } from 'react';
import '../../styles/components/user-dashboard/UserOverview.css';
import UserDashboardLayout from './UserDashboardLayout';
import UserSideNav from './UserSideNav';

// Import icons
import trendUpIcon from '../../assets/trend-up.png';
import informationCircleIcon from '../../assets/information-circle.png';
import cardCoinIcon from '../../assets/card-coin.png';

interface UserOverviewProps {
  onNavigateToSignUp?: () => void;
  onNavigateToLogin?: () => void;
  onNavigateToLanding?: () => void;
}

const UserOverview: React.FC<UserOverviewProps> = ({
  onNavigateToSignUp,
  onNavigateToLogin,
  onNavigateToLanding
}) => {
  const [activeTab, setActiveTab] = useState('overview');
  const [showBalances, setShowBalances] = useState(true);
  const [hideBalances, setHideBalances] = useState(false);

  // Toggle balance visibility
  const toggleBalances = () => {
    setShowBalances(!showBalances);
    setHideBalances(!hideBalances);
  };

  return (
    <UserDashboardLayout className="user-overview-container">
      <div className="user-overview-content">
        {/* User Greeting Header */}
        <div className="user-header">
          <div className="user-greeting">
            <h1 className="greeting-text">Hi, Oluwatosin 👋</h1>
            <p className="greeting-subtitle">Here is your staking overview</p>

            <div className="header-controls">
              <div className="balance-toggle">
                <span className="toggle-label">Show balances</span>
                <label className="toggle-switch">
                  <input
                    type="checkbox"
                    checked={showBalances}
                    onChange={toggleBalances}
                  />
                  <span className="toggle-slider"></span>
                </label>
                <span className="toggle-label">Hide balances</span>
              </div>
            </div>
          </div>
        </div>

        {/* Stake Button - Outside red section */}
        <div className="stake-button-section">
          <button className="stake-esvc-btn">
            <img src={cardCoinIcon} alt="Stake" className="btn-icon" />
            Stake ESVC
          </button>
        </div>

        <div className="dashboard-layout">
          {/* Sidebar */}
          <UserSideNav
            activeTab={activeTab}
            onTabChange={setActiveTab}
          />

          {/* Dashboard Content */}
          <div className="dashboard-content">
            {/* General Summary */}
            <div className="summary-section">
              <h2 className="section-title">General Summary (From Your 7 Wallets)</h2>

              <div className="summary-grid">
                <div className="summary-card">
                  <div className="card-header">
                    <span className="card-label">TOTAL ESVC STAKED</span>
                  </div>
                  <div className="card-value">
                    {showBalances ? '788.50' : '***.**'}
                    <span className="card-unit">ESVC</span>
                  </div>
                  <div className="card-header">
                    <span className="card-label">CURRENT STAKED ESVC VALUE</span>
                  </div>
                  <div className="card-value">
                    {showBalances ? '$13,700' : '$***,***'}
                  </div>
                </div>

                <div className="summary-card">
                  <div className="card-header">
                    <span className="card-label">ROI EARNED SO FAR</span>
                  </div>
                  <div className="card-value">
                    {showBalances ? '$700' : '$***'}
                  </div>
                  <div className="card-change positive">
                    <img src={trendUpIcon} alt="Trend up" className="change-icon" />
                    + 4.8% Today
                  </div>
                </div>

                <div className="summary-card">
                  <div className="card-header">
                    <span className="card-label">ROI PAID SO FAR</span>
                  </div>
                  <div className="card-value">
                    {showBalances ? '$660' : '$***'}
                  </div>
                </div>
              </div>
            </div>

            {/* Individual Wallet Summary */}
            <div className="wallet-summary">
              <h2 className="section-title">Individual Wallet Summary</h2>

              <div className="wallet-header-row">
                <div className="wallet-selector">
                  <select className="wallet-dropdown">
                    <option value="wallet1">🔒 Wallet 1 ($10,000 stakes)</option>
                  </select>
                </div>

                <div className="wallet-info">
                  <img src={informationCircleIcon} alt="Info" className="info-icon" />
                  <span className="info-text">
                    Every new stake you make gets its own dedicated wallet for added transparency and traceability.
                  </span>
                </div>
              </div>

              <div className="wallet-stats-grid">
                <div className="wallet-stat-card">
                  <div className="card-header">
                    <span className="card-label">TOTAL ESVC STAKED</span>
                  </div>
                  <div className="card-content">
                    <div className="card-value">
                      {showBalances ? '788.50' : '***.**'}
                      <span className="card-unit">ESVC</span>
                    </div>
                    <div className="card-change positive">
                      <img src={trendUpIcon} alt="Trend up" className="change-icon" />
                      + 4.8%
                    </div>
                  </div>
                </div>

                <div className="wallet-stat-card">
                  <div className="card-header">
                    <span className="card-label">CURRENT STAKED ESVC VALUE</span>
                  </div>
                  <div className="card-content">
                    <div className="card-value">
                      {showBalances ? '$13,700' : '$***,***'}
                    </div>
                    <div className="card-change positive">
                      <img src={trendUpIcon} alt="Trend up" className="change-icon" />
                      + 4.8%
                    </div>
                    <div className="card-note">$10,000 at Deposit</div>
                  </div>
                </div>

                <div className="wallet-stat-card">
                  <div className="card-content-dual">
                    <div className="card-section">
                      <div className="card-header">
                        <span className="card-label">ROI EARNED SO FAR</span>
                      </div>
                      <div className="card-value">
                        {showBalances ? '$700' : '$***'}
                      </div>
                    </div>
                    <div className="card-section">
                      <div className="card-header">
                        <span className="card-label">ROI PAID SO FAR</span>
                      </div>
                      <div className="card-value">
                        {showBalances ? '$660' : '$***'}
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* ROI Summary - Inside the wallet card */}
              <div className="roi-section">
                <div className="roi-header">
                  <h3 className="roi-title">ROI Summary</h3>
                  <div className="roi-available">${showBalances ? '8.3' : '***'} ({showBalances ? '11.2832' : '***'} ESVC) Available</div>
                </div>

                <div className="roi-progress">
                  <div className="progress-bar">
                    <div className="progress-fill" style={{ width: '35%' }}></div>
                  </div>
                  <div className="progress-labels">
                    <span className="progress-earned">${showBalances ? '700' : '***'} ROI earned so far</span>
                    <span className="progress-expected">${showBalances ? '2,000' : '***'} Total Expected ROI</span>
                  </div>
                </div>

                <button className="withdraw-roi-btn">Withdraw Earned ROI</button>

                {/* ROI Info - Inside the ROI section */}
                <div className="roi-info">
                  <img src={informationCircleIcon} alt="Info" className="info-icon" />
                  <span className="info-text">Minimum withdrawal amount is $10</span>
                </div>
              </div>

              {/* Unstake Section - Inside the wallet card, no separate card */}
              <div className="unstake-section-inner">
                <h3 className="unstake-title">Unstake</h3>

                <div className="unstake-grid">
                  <div className="unstake-info">
                    <div className="unstake-item">
                      <span className="unstake-label">DATE STAKED</span>
                      <span className="unstake-value">Jan 3, 2025</span>
                    </div>
                    <div className="unstake-item">
                      <span className="unstake-label">UNSTAKE DATE</span>
                      <span className="unstake-value">Jan 3, 2026</span>
                    </div>
                    <div className="unstake-item">
                      <span className="unstake-label">DAYS LEFT UNTIL UNSTAKE</span>
                      <span className="unstake-value">267 Days</span>
                    </div>
                  </div>

                  <button className="unstake-btn">Unstake</button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </UserDashboardLayout>
  );
};

export default UserOverview;
