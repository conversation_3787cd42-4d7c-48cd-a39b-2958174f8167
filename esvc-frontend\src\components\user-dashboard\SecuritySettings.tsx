import React, { useState } from 'react';
import UserDashboardLayout from './UserDashboardLayout';
import UserSideNav from './UserSideNav';
import '../../styles/components/user-dashboard/SecuritySettings.css';

// Import assets
// import cardCoinIcon from '../../assets/card-coin.png';

const SecuritySettings: React.FC = () => {
  const [activeTab, setActiveTab] = useState('security-settings');
  const [showBalances, setShowBalances] = useState(true);

  const toggleBalances = () => {
    setShowBalances(!showBalances);
  };

  return (
    <UserDashboardLayout className="security-settings-container">
      <div className="security-settings-content">
        {/* User Greeting Header */}
        <div className="user-header">
          <div className="user-greeting">
            <h1 className="greeting-text">Hi, <PERSON><PERSON><PERSON><PERSON><PERSON> 👋</h1>
            <p className="greeting-subtitle">Manage your security settings</p>

            <div className="header-controls">
              <button className="stake-esvc-btn">
                <img src={cardCoinIcon} alt="Stake" className="btn-icon" />
                Stake ESVC
              </button>
              
              <div className="balance-toggle">
                <span className="toggle-label">Show balances</span>
                <label className="toggle-switch">
                  <input
                    type="checkbox"
                    checked={showBalances}
                    onChange={toggleBalances}
                  />
                  <span className="toggle-slider"></span>
                </label>
                <span className="toggle-label">Hide balances</span>
              </div>
            </div>
          </div>
        </div>

        <div className="dashboard-layout">
          <UserSideNav activeTab={activeTab} onTabChange={setActiveTab} />
          
          <div className="dashboard-content">
            <div className="security-section">
              <div className="security-card">
                <h2 className="section-title">Security Settings</h2>
                <p className="section-description">
                  Manage your account security and privacy settings.
                </p>
                
                <div className="settings-grid">
                  <div className="setting-item">
                    <h3 className="setting-title">Two-Factor Authentication</h3>
                    <p className="setting-description">Add an extra layer of security to your account</p>
                    <button className="setting-btn">Enable 2FA</button>
                  </div>
                  
                  <div className="setting-item">
                    <h3 className="setting-title">Change Password</h3>
                    <p className="setting-description">Update your account password</p>
                    <button className="setting-btn">Change Password</button>
                  </div>
                  
                  <div className="setting-item">
                    <h3 className="setting-title">Login History</h3>
                    <p className="setting-description">View your recent login activity</p>
                    <button className="setting-btn">View History</button>
                  </div>
                  
                  <div className="setting-item">
                    <h3 className="setting-title">Account Recovery</h3>
                    <p className="setting-description">Set up account recovery options</p>
                    <button className="setting-btn">Setup Recovery</button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </UserDashboardLayout>
  );
};

export default SecuritySettings;
